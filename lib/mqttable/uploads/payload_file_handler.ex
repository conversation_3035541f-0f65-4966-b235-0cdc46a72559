defmodule Mqttable.Uploads.PayloadFileHandler do
  @moduledoc """
  Module for handling payload file uploads.
  Provides centralized functionality for processing and managing payload files.
  """
  require Logger

  # Payload files directory constants
  @payload_files_dir "priv/data/payload_files"

  # Upload options for payload files
  @upload_options [
    accept: :any,
    max_entries: 1,
    # 16MB maximum file size for payload files
    max_file_size: 16_000_000
  ]

  @doc """
  Returns the standard upload options for payload files.
  """
  def upload_options, do: @upload_options

  @doc """
  Returns the path to the payload files directory.
  """
  def payload_files_dir, do: @payload_files_dir

  @doc """
  Ensures the payload files directory exists.
  """
  def ensure_payload_files_dir do
    payload_files_dir = Path.join([:code.priv_dir(:mqttable), "data", "payload_files"])
    File.mkdir_p!(payload_files_dir)
    payload_files_dir
  end

  @doc """
  Processes an uploaded payload file.

  ## Parameters
  - `socket`: The LiveView socket
  - `upload_name`: The name of the upload field (e.g., :payload_file)
  - `existing_path`: The existing file path (if any)

  ## Returns
  - `{path, socket}`: The path to the uploaded file and the updated socket
  """
  def process_uploaded_file(socket, upload_name, existing_path) do
    # Ensure the payload files directory exists
    payload_files_dir = ensure_payload_files_dir()

    # Check if there are any uploaded entries for this upload name
    uploaded_files =
      Phoenix.LiveView.Upload.consume_uploaded_entries(socket, upload_name, fn %{path: path},
                                                                               entry ->
        # Generate a unique filename with the original file extension
        original_filename = entry.client_name
        # Extract the file extension
        ext = Path.extname(original_filename)
        # Generate a unique filename with timestamp
        now = DateTime.utc_now()

        formatted_timestamp =
          "#{now.year}#{pad_number(now.month)}#{pad_number(now.day)}#{pad_number(now.hour)}#{pad_number(now.minute)}#{pad_number(now.second)}_#{pad_number(now.microsecond |> elem(0), 6)}"

        # Create unique filename for payload file
        unique_name = "payload_#{formatted_timestamp}#{ext}"

        # Create destination path in payload files directory
        dest = Path.join(payload_files_dir, unique_name)

        # Copy the file to the destination
        File.cp!(path, dest)

        # Return the path relative to the payload files directory
        {:ok, "/payload_files/#{unique_name}"}
      end)

    # If there are uploaded files, use the first one, otherwise keep the existing path
    case uploaded_files do
      [path | _] -> {path, socket}
      [] -> {existing_path, socket}
    end
  end

  @doc """
  Reads a payload file and returns its content as base64.

  ## Parameters
  - `file_path`: The relative file path (e.g., "/payload_files/payload_20241217_123456.txt")

  ## Returns
  - `{:ok, base64_content}`: On success, returns the file content as base64
  - `{:error, reason}`: On failure, returns the error reason
  """
  def read_file_as_base64(file_path) when is_binary(file_path) do
    # Convert relative path to absolute path
    absolute_path = get_absolute_path(file_path)

    case File.read(absolute_path) do
      {:ok, content} ->
        base64_content = Base.encode64(content)
        {:ok, base64_content}

      {:error, reason} ->
        Logger.warning("Failed to read payload file #{file_path}: #{reason}")
        {:error, "Failed to read file: #{reason}"}
    end
  end

  def read_file_as_base64(_), do: {:error, "Invalid file path"}

  @doc """
  Cleans up unused payload files in the payload files directory.

  ## Parameters
  - `ui_state`: The UI state containing send_modal_forms with payload file references
  - `max_age_hours`: Maximum age in hours for unused files before deletion (default: 1)
  """
  def cleanup_unused_payload_files(ui_state, max_age_hours \\ 1) do
    # Get the payload files directory path
    payload_files_dir = Path.join([:code.priv_dir(:mqttable), "data", "payload_files"])

    # Ensure the directory exists
    unless File.exists?(payload_files_dir) do
      # Skip cleanup if directory doesn't exist
      :ok
    else
      # Extract all payload file paths from UI state
      payload_filenames =
        ui_state
        |> Map.get(:send_modal_forms, %{})
        |> Enum.flat_map(fn {_broker_name, form_state} ->
          payload_file = Map.get(form_state, "payload_file", "")

          if is_binary(payload_file) && payload_file != "" do
            [Path.basename(payload_file)]
          else
            []
          end
        end)
        |> Enum.uniq()

      # Get all files in the payload files directory
      case File.ls(payload_files_dir) do
        {:ok, all_files} ->
          # Check which files are not referenced in any form state
          unused_files = all_files -- payload_filenames

          # Only proceed if there are unused files to check
          unless Enum.empty?(unused_files) do
            # Calculate the age threshold in seconds
            age_threshold_seconds = max_age_hours * 3600
            current_time = :os.system_time(:second)

            # Check each unused file
            Enum.each(unused_files, fn file ->
              file_path = Path.join(payload_files_dir, file)

              case File.stat(file_path, time: :posix) do
                {:ok, stat} ->
                  creation_time = stat.ctime
                  age_in_seconds = current_time - creation_time

                  # Delete files older than the threshold
                  if age_in_seconds > age_threshold_seconds do
                    case File.rm(file_path) do
                      :ok ->
                        Logger.info(
                          "Deleted unused payload file: #{file} (age: #{age_in_seconds / 3600} hours)"
                        )

                      {:error, _reason} ->
                        # Just skip files we can't delete
                        :ok
                    end
                  end

                {:error, _reason} ->
                  # Skip files we can't stat
                  :ok
              end
            end)
          end

        {:error, reason} ->
          Logger.error("Error listing files in payload files directory: #{reason}")
      end
    end
  end

  # Private helper functions

  defp get_absolute_path(file_path) do
    # Remove leading slash if present
    clean_path = String.trim_leading(file_path, "/")

    # Extract filename from path like "payload_files/filename.ext"
    filename =
      case String.split(clean_path, "/") do
        ["payload_files", filename] -> filename
        [filename] -> filename
        _ -> Path.basename(clean_path)
      end

    # Build absolute path
    payload_files_dir = Path.join([:code.priv_dir(:mqttable), "data", "payload_files"])
    Path.join(payload_files_dir, filename)
  end

  defp pad_number(number, length \\ 2) do
    number
    |> Integer.to_string()
    |> String.pad_leading(length, "0")
  end
end
