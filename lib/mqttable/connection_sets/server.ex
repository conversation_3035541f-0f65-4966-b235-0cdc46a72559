defmodule Mqttable.ConnectionSets.Server do
  @moduledoc """
  GenServer for managing connection sets state.
  Provides in-memory storage with periodic persistence to disk.
  Broadcasts changes to all connected clients.
  """
  use GenServer
  require Logger

  alias Mqttable.ConnectionSets.Storage
  alias Mqttable.Uploads.<PERSON><PERSON><PERSON><PERSON>
  alias Mqttable.Uploads.PayloadFile<PERSON><PERSON>ler
  alias <PERSON>.PubSub

  # Save every 1 minutes
  @save_interval :timer.minutes(1)
  @pubsub_topic "brokers"

  # Run cleanup every 60 save intervals
  @cleanup_interval 60

  # Client API

  @doc """
  Starts the connection sets server.
  """
  def start_link(opts \\ []) do
    GenServer.start_link(__MODULE__, opts, name: __MODULE__)
  end

  @doc """
  Gets all connection sets.
  """
  def get_all do
    GenServer.call(__MODULE__, :get_all)
  end

  @doc """
  Gets the UI state.
  """
  def get_ui_state do
    GenServer.call(__MODULE__, :get_ui_state)
  end

  @doc """
  Updates the connection sets.
  """
  def update(connection_sets) do
    GenServer.cast(__MODULE__, {:update, connection_sets})
  end

  @doc """
  Updates the UI state.
  """
  def update_ui_state(ui_state) do
    GenServer.cast(__MODULE__, {:update_ui_state, ui_state})
  end

  @doc """
  Forces an immediate save to disk.
  """
  def save_to_disk do
    GenServer.cast(__MODULE__, :save_to_disk)
  end

  # Server Callbacks

  @impl true
  def init(_opts) do
    # Load brokers from disk
    {connection_sets, ui_state} =
      case Storage.load() do
        {:ok, loaded_sets, loaded_ui_state} ->
          {loaded_sets, loaded_ui_state}

        {:error, reason} ->
          Logger.error("Failed to load connection sets: #{reason}")
          {[], %{expanded_sets: %{}}}
      end

    schedule_periodic_task()

    {:ok,
     %{
       connection_sets: connection_sets,
       ui_state: ui_state,
       dirty: false,
       save_counter: 0
     }, {:continue, :start_saved_connections}}
  end

  @impl true
  def handle_continue(:start_saved_connections, %{connection_sets: connection_sets} = state) do
    # Start workers for all saved connections after server is fully initialized
    Logger.info("Starting saved connections after ConnectionSets server initialization")
    Mqttable.MqttClient.Manager.start_saved_connections(connection_sets)
    {:noreply, state}
  end

  @impl true
  def handle_call(:get_all, _from, state) do
    {:reply, state.connection_sets, state}
  end

  @impl true
  def handle_call(:get_ui_state, _from, state) do
    {:reply, state.ui_state, state}
  end

  @impl true
  def handle_cast({:update, connection_sets}, state) do
    # If the connection sets have changed, broadcast the update to all clients
    dirty =
      if connection_sets != state.connection_sets do
        PubSub.broadcast(
          Mqttable.PubSub,
          @pubsub_topic,
          {:connection_sets_updated, connection_sets}
        )

        true
      else
        state.dirty
      end

    {:noreply, %{state | connection_sets: connection_sets, dirty: dirty}}
  end

  @impl true
  def handle_cast({:update_ui_state, ui_state}, state) do
    # Mark state as dirty if UI state has changed
    dirty = ui_state != state.ui_state || state.dirty

    # If the UI state has changed, broadcast the update to all clients
    if ui_state != state.ui_state do
      PubSub.broadcast(
        Mqttable.PubSub,
        @pubsub_topic,
        {:ui_state_updated, ui_state}
      )
    end

    {:noreply, %{state | ui_state: ui_state, dirty: dirty}}
  end

  @impl true
  def handle_cast(:save_to_disk, state) do
    # Only save if state is dirty
    new_state =
      if state.dirty do
        save_data(state.connection_sets, state.ui_state)
        %{state | dirty: false}
      else
        state
      end

    {:noreply, new_state}
  end

  @impl true
  def handle_info(:periodic_task, state) do
    # Increment the save counter
    new_counter = state.save_counter + 1

    # Check if state is dirty and save if needed
    new_state =
      if state.dirty do
        save_data(state.connection_sets, state.ui_state)
        %{state | dirty: false, save_counter: new_counter}
      else
        %{state | save_counter: new_counter}
      end

    # Check if we need to run cleanup (every @cleanup_interval times)
    new_state =
      if new_counter >= @cleanup_interval do
        # Clean up unused certificate files
        CertificateHandler.cleanup_unused_certificates(state.connection_sets)
        # Clean up unused payload files
        PayloadFileHandler.cleanup_unused_payload_files(state.ui_state)
        %{new_state | save_counter: 0}
      else
        new_state
      end

    # Reschedule the periodic task
    schedule_periodic_task()

    {:noreply, new_state}
  end

  @impl true
  def terminate(_reason, state) do
    # Save connection sets when the server terminates
    if state.dirty do
      save_data(state.connection_sets, state.ui_state)
    end

    :ok
  end

  # Private functions

  defp save_data(connection_sets, ui_state) do
    case Storage.save(connection_sets, ui_state) do
      {:ok, _path} ->
        Logger.info("Connection sets and UI state saved to disk")
        :ok

      {:error, reason} ->
        Logger.error("Failed to save data: #{reason}")
        :error
    end
  end

  defp schedule_periodic_task do
    Process.send_after(self(), :periodic_task, @save_interval)
  end
end
