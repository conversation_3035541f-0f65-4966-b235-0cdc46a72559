// Payload File Upload Hook for Phoenix LiveView
// Handles file selection and converts to base64 for server processing

const PayloadFileUpload = {
  mounted() {
    console.log('PayloadFileUpload Hook mounted on element:', this.el);
    this.el.addEventListener('change', (event) => {
      console.log('File input change event triggered:', event);
      const file = event.target.files[0];

      if (!file) {
        return;
      }

      // Check file size (16MB limit)
      const maxSize = 16 * 1024 * 1024; // 16MB
      if (file.size > maxSize) {
        this.showError(`File too large. Maximum size is 16MB, but file is ${this.formatFileSize(file.size)}.`);
        this.el.value = ''; // Clear the file input
        return;
      }

      // Show upload progress
      this.showProgress(file.name, 0);

      // Read file and convert to base64
      const reader = new FileReader();

      reader.onload = (e) => {
        try {
          // Get base64 content (remove data:mime;base64, prefix)
          const base64Content = e.target.result.split(',')[1];

          // Hide progress
          this.hideProgress();

          // Notify the LiveView component about the file data
          console.log('Sending file_uploaded event:', {
            file_name: file.name,
            file_size: file.size,
            file_type: file.type,
            file_content_length: base64Content.length
          });

          this.pushEvent("file_uploaded", {
            file_name: file.name,
            file_size: file.size,
            file_type: file.type,
            file_content: base64Content
          });

          // Clear the file input
          this.el.value = '';
        } catch (error) {
          console.error('Error processing file:', error);
          this.hideProgress();
          this.showError('Failed to process file. Please try again.');
          this.el.value = '';
        }
      };

      reader.onerror = () => {
        this.hideProgress();
        this.showError('Failed to read file. Please try again.');
        this.el.value = '';
      };

      // Read file as data URL (base64)
      reader.readAsDataURL(file);
    });
  },



  // Show upload progress
  showProgress(fileName, percent) {
    // Find or create progress container
    let progressContainer = this.el.parentElement.querySelector('.upload-progress');
    
    if (!progressContainer) {
      progressContainer = document.createElement('div');
      progressContainer.className = 'upload-progress mt-2';
      this.el.parentElement.appendChild(progressContainer);
    }

    progressContainer.innerHTML = `
      <div class="text-xs text-base-content/60 mb-1">
        Uploading: ${fileName} (${percent}%)
      </div>
      <progress class="progress progress-primary w-full" value="${percent}" max="100"></progress>
    `;
  },

  // Hide upload progress
  hideProgress() {
    const progressContainer = this.el.parentElement.querySelector('.upload-progress');
    if (progressContainer) {
      progressContainer.remove();
    }
  },

  // Show error message
  showError(message) {
    // Find or create error container
    let errorContainer = this.el.parentElement.querySelector('.upload-error');
    
    if (!errorContainer) {
      errorContainer = document.createElement('div');
      errorContainer.className = 'upload-error alert alert-error mt-3 text-xs';
      this.el.parentElement.appendChild(errorContainer);
    }

    errorContainer.textContent = message;

    // Auto-hide error after 5 seconds
    setTimeout(() => {
      if (errorContainer && errorContainer.parentElement) {
        errorContainer.remove();
      }
    }, 5000);
  },

  // Helper function to format file size
  formatFileSize(bytes) {
    if (bytes === 0) return '0 Bytes';

    const k = 1024;
    const sizes = ['Bytes', 'KB', 'MB', 'GB'];
    const i = Math.floor(Math.log(bytes) / Math.log(k));

    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
  }
};

export default PayloadFileUpload;
